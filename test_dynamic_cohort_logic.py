#!/usr/bin/env python3
"""
Test script to validate dynamic cohort logic calculations.
"""

from datetime import datetime, <PERSON><PERSON><PERSON>


def test_dynamic_cohort_logic():
    """Test the dynamic cohort calculation logic."""

    print("🧪 Testing Dynamic Cohort Logic")
    print("=" * 50)

    # Base cohort period (fixed) - Week 1 starting April 24, 2025
    base_cohort_start = "2025-04-24"
    base_cohort_end = "2025-04-30"
    base_cohort_dt = datetime.strptime(base_cohort_start, "%Y-%m-%d")

    print(f"📅 Base Cohort Period (Week 1): {base_cohort_start} to {base_cohort_end}")
    print(f"📅 Base Cohort Date Object: {base_cohort_dt}")

    # Test different execution dates
    test_dates = [
        datetime(2025, 4, 24),  # Same as cohort start (Week 1)
        datetime(2025, 4, 30),  # Same as cohort end (Week 1)
        datetime(2025, 5, 7),  # 1 week after cohort (Week 2)
        datetime(2025, 5, 14),  # 2 weeks after cohort (Week 3)
        datetime(2025, 6, 11),  # 4 weeks after cohort (Week 5)
        datetime(2025, 7, 2),  # Current date (~10 weeks after)
        datetime(2025, 12, 25),  # Far future (~35 weeks after)
    ]

    print("\n🔍 Testing Different Execution Dates:")
    print("-" * 70)
    print(
        f"{'Execution Date':<15} {'Days Since':<12} {'Weeks Since':<12} {'Analysis Weeks':<15}"
    )
    print("-" * 70)

    for execution_date in test_dates:
        # Calculate weeks since cohort
        days_since = (execution_date - base_cohort_dt).days
        weeks_since_cohort = days_since // 7

        # Calculate analysis weeks (same logic as implementation)
        analysis_weeks = max(1, min(weeks_since_cohort + 1, 12))

        print(
            f"{execution_date.strftime('%Y-%m-%d'):<15} {days_since:<12} {weeks_since_cohort:<12} {analysis_weeks:<15}"
        )

    print("\n📊 Expected Behavior:")
    print("- Week 1: April 24-30, 2025 (Earliest Cohort)")
    print("- Week 2: May 1-7, 2025")
    print("- Week 3: May 8-14, 2025")
    print("- ...")
    print("- Analysis weeks capped at 12 for performance")
    print(
        "- Cohort history capped with earliest cohort starting at Week 1 (April 24-30, 2025)"
    )

    print("\n✅ Dynamic cohort logic validated!")


if __name__ == "__main__":
    test_dynamic_cohort_logic()
